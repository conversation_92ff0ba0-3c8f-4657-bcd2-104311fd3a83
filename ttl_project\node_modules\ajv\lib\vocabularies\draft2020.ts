import type {Vocabulary} from "../types"
import coreVocabulary from "./core"
import validationVocabulary from "./validation"
import getApplicatorVocabulary from "./applicator"
import dynamicVocabulary from "./dynamic"
import nextVocabulary from "./next"
import unevaluatedVocabulary from "./unevaluated"
import formatVocabulary from "./format"
import {metadataVocabulary, contentVocabulary} from "./metadata"

const draft2020Vocabularies: Vocabulary[] = [
  dynamicVocabulary,
  coreVocabulary,
  validationVocabulary,
  getApplicatorVocabulary(true),
  formatVocabulary,
  metadataVocabulary,
  contentVocabulary,
  nextVocabulary,
  unevaluatedVocabulary,
]

export default draft2020Vocabularies
