{"name": "expo-build-properties", "version": "1.0.9", "description": "Config plugin to customize native build properties on prebuild", "main": "build/withBuildProperties.js", "types": "build/withBuildProperties.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "build", "build-properties"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-build-properties"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/build-properties", "dependencies": {"ajv": "^8.11.0", "semver": "^7.6.0"}, "devDependencies": {"expo-module-scripts": "^5.0.7"}, "peerDependencies": {"expo": "*"}, "gitHead": "0d9ae61f3dea2e2b854576859e5b50fca5503fc1"}