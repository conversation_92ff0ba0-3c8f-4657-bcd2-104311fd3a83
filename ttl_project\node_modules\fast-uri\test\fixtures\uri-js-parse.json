[["//www.g.com/error\n/bleh/bleh", {"host": "www.g.com", "path": "/error%0A/bleh/bleh", "reference": "relative"}], ["https://fastify.org", {"scheme": "https", "host": "fastify.org", "path": "", "reference": "absolute"}], ["/definitions/Record%3Cstring%2CPerson%3E", {"path": "/definitions/Record%3Cstring%2CPerson%3E", "reference": "relative"}], ["//***********", {"host": "***********", "path": "", "reference": "relative"}], ["//************", {"host": "**********", "path": "", "reference": "relative"}], ["//[2001:db8::7%en0]", {"host": "2001:db8::7%en0", "path": "", "reference": "relative"}], ["//[2001:dbZ::1]:80", {"host": "[2001:dbz::1]", "port": 80, "path": "", "reference": "relative"}], ["//[2001:db8::1]:80", {"host": "2001:db8::1", "port": 80, "path": "", "reference": "relative"}], ["//[2001:db8::001]:80", {"host": "2001:db8::1", "port": 80, "path": "", "reference": "relative"}], ["uri://user:<EMAIL>:123/one/two.three?q1=a1&q2=a2#body", {"scheme": "uri", "userinfo": "user:pass", "host": "example.com", "port": 123, "path": "/one/two.three", "query": "q1=a1&q2=a2", "fragment": "body", "reference": "uri"}], ["http://user:<EMAIL>:123/one/space in.url?q1=a1&q2=a2#body", {"scheme": "http", "userinfo": "user:pass", "host": "example.com", "port": 123, "path": "/one/space%20in.url", "query": "q1=a1&q2=a2", "fragment": "body", "reference": "uri"}], ["http://User:<EMAIL>:123/one/space in.url?q1=a1&q2=a2#body", {"scheme": "http", "userinfo": "User:Pass", "host": "example.com", "port": 123, "path": "/one/space%20in.url", "query": "q1=a1&q2=a2", "fragment": "body", "reference": "uri"}], ["http://<EMAIL>:123/one/space", {"scheme": "http", "userinfo": "A%3AB", "host": "example.com", "port": 123, "path": "/one/space", "reference": "absolute"}], ["//[::ffff:*************]", {"host": "::ffff:*************", "path": "", "reference": "relative"}], ["uri://***********.example.com/en/process", {"scheme": "uri", "host": "***********.example.com", "path": "/en/process", "reference": "absolute"}], ["//[2606:2800:220:1:248:1893:25c8:1946]/test", {"host": "2606:2800:220:1:248:1893:25c8:1946", "path": "/test", "reference": "relative"}], ["ws://example.com/chat", {"scheme": "ws", "host": "example.com", "reference": "absolute", "secure": false, "resourceName": "/chat"}], ["ws://example.com/foo?bar=baz", {"scheme": "ws", "host": "example.com", "reference": "absolute", "secure": false, "resourceName": "/foo?bar=baz"}], ["wss://example.com/?bar=baz", {"scheme": "wss", "host": "example.com", "reference": "absolute", "secure": true, "resourceName": "/?bar=baz"}], ["wss://example.com/chat", {"scheme": "wss", "host": "example.com", "reference": "absolute", "secure": true, "resourceName": "/chat"}], ["wss://example.com/foo?bar=baz", {"scheme": "wss", "host": "example.com", "reference": "absolute", "secure": true, "resourceName": "/foo?bar=baz"}], ["wss://example.com/?bar=baz", {"scheme": "wss", "host": "example.com", "reference": "absolute", "secure": true, "resourceName": "/?bar=baz"}], ["urn:uuid:f81d4fae-7dec-11d0-a765-00a0c91e6bf6", {"scheme": "urn", "reference": "absolute", "nid": "uuid", "uuid": "f81d4fae-7dec-11d0-a765-00a0c91e6bf6"}], ["urn:uuid:notauuid-7dec-11d0-a765-00a0c91e6bf6", {"scheme": "urn", "reference": "absolute", "nid": "uuid", "uuid": "notauuid-7dec-11d0-a765-00a0c91e6bf6", "error": "UUID is not valid."}], ["urn:example:%D0%B0123,z456", {"scheme": "urn", "reference": "absolute", "nid": "example", "nss": "%D0%B0123,z456"}], ["//[2606:2800:220:1:248:1893:25c8:1946:43209]", {"host": "[2606:2800:220:1:248:1893:25c8:1946:43209]", "path": "", "reference": "relative"}], ["http://foo.bar", {"scheme": "http", "host": "foo.bar", "path": "", "reference": "absolute"}], ["http://", {"scheme": "http", "host": "", "path": "", "reference": "absolute", "error": "HTTP URIs must have a host."}], ["#/$defs/stringMap", {"path": "", "fragment": "/$defs/stringMap", "reference": "same-document"}], ["#/$defs/string%20Map", {"path": "", "fragment": "/$defs/string%20Map", "reference": "same-document"}], ["#/$defs/string Map", {"path": "", "fragment": "/$defs/string%20Map", "reference": "same-document"}], ["//?json=%7B%22foo%22%3A%22bar%22%7D", {"host": "", "path": "", "query": "json=%7B%22foo%22%3A%22bar%22%7D", "reference": "relative"}], ["mailto:<EMAIL>", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"]}], ["mailto:<EMAIL>?subject=current-issue", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "subject": "current-issue"}], ["mailto:<EMAIL>?body=send%20current-issue", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "body": "send current-issue"}], ["mailto:<EMAIL>?body=send%20current-issue%0D%0Asend%20index", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "body": "send current-issue\r\nsend index"}], ["mailto:<EMAIL>?In-Reply-To=%<EMAIL>%3E", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "headers": {"In-Reply-To": "<<EMAIL>>"}}], ["mailto:<EMAIL>?body=subscribe%20bamboo-l", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "body": "subscribe bamboo-l"}], ["mailto:<EMAIL>?cc=<EMAIL>&body=hello", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "body": "hello", "headers": {"cc": "<EMAIL>"}}], ["mailto:<EMAIL>", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"]}], ["mailto:<EMAIL>?blat=foop", {"scheme": "mailto", "reference": "absolute", "to": ["unlikely?<EMAIL>"], "headers": {"blat": "foop"}}], ["mailto:<PERSON>%<EMAIL>", {"scheme": "mailto", "reference": "absolute", "to": ["Mike&<PERSON>@example.org"]}], ["mailto:%<EMAIL>", {"scheme": "mailto", "reference": "absolute", "to": ["\"not@me\"@example.org"]}], ["mailto:%<EMAIL>", {"scheme": "mailto", "reference": "absolute", "to": ["\"oh\\\\no\"@example.org"]}], ["mailto:%22%5C%5C%5C%22it'<EMAIL>", {"scheme": "mailto", "reference": "absolute", "to": ["\"\\\\\\\"it's\\ ugly\\\\\\\"\"@example.org"]}], ["mailto:<EMAIL>?subject=caf%C3%A9", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "subject": "café"}], ["mailto:<EMAIL>?subject=%3D%3Futf-8%3FQ%3Fcaf%3DC3%3DA9%3F%3D", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "subject": "=?utf-8?Q?caf=C3=A9?="}], ["mailto:<EMAIL>?subject=%3D%3Fiso-8859-1%3FQ%3Fcaf%3DE9%3F%3D", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "subject": "=?iso-8859-1?Q?caf=E9?="}], ["mailto:<EMAIL>?subject=caf%C3%A9&body=caf%C3%A9", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "subject": "café", "body": "café"}], ["mailto:user@%E7%B4%8D%E8%B1%86.example.org?subject=Test&body=NATTO", {"scheme": "mailto", "reference": "absolute", "to": ["<EMAIL>"], "subject": "Test", "body": "NATTO"}]]